import {
  AgentMediaRepository,
  AgentProductRepository,
  AgentRankRepository,
  AgentRepository,
  AgentUserRepository,
  TypeAgentRepository,
  UserMultiAgentRepository
} from '@modules/agent/repositories';

import { UserProductRepository } from '@/modules/business/repositories';
import { AppException } from '@common/exceptions';
import { Agent, AgentUser } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { FacebookPageRepository, UserWebsiteRepository } from '@modules/integration/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import { S3Service } from '@shared/services/s3.service';
import { FileSizeEnum, ImageType, TimeIntervalEnum } from '@shared/utils';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { Transactional } from 'typeorm-transactional';
import { CreateAgentResponseDto } from '../dto/agent';
import { CreateAgentDto } from '../dto/agent/create-agent.dto';
import { TypeAgentValidationHelper } from '../helpers/type-agent-validation.helper';
import { ProfileMapper } from '../mappers';

/**
 * Service xử lý các thao tác liên quan đến agent cho người dùng
 */
@Injectable()
export class AgentUserService {
  private readonly logger = new Logger(AgentUserService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentUserRepository: AgentUserRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly urlRepository: UrlRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly userWebsiteRepository: UserWebsiteRepository,
    private readonly facebookPageRepository: FacebookPageRepository,
    private readonly facebookService: FacebookService,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly agentRankRepository: AgentRankRepository,
    private readonly userMultiAgentRepository: UserMultiAgentRepository,
    private readonly typeAgentValidationHelper: TypeAgentValidationHelper,
  ) { }

  /**
   * Tạo agent mới với cấu trúc modular
   * @param userId ID của người dùng
   * @param createDto Thông tin agent mới theo cấu hình TypeAgent
   * @returns Thông tin tạo agent thành công
   */
  @Transactional()
  async createAgent(
    userId: number,
    createDto: CreateAgentDto,
  ): Promise<CreateAgentResponseDto> {
    try {
      this.logger.log(`Bắt đầu tạo agent cho user ${userId}: ${createDto.name}`);

      // 1. Lấy TypeAgent và TypeAgentConfig từ typeId
      const typeAgent = await this.typeAgentRepository.findById(createDto.typeId);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      const typeAgentConfig = typeAgent.config; // TypeAgentConfig từ trường config
      this.logger.log(`TypeAgentConfig: ${JSON.stringify(typeAgentConfig)}`);

      // 2. Validate dữ liệu dựa trên TypeAgentConfig
      await this.validateAgentDataByConfig(createDto, typeAgentConfig);

      // 3. Kiểm tra tên agent đã tồn tại cho user này chưa
      const existingAgent = await this.agentRepository.existsByNameAndUserId(createDto.name, userId);
      if (existingAgent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
      }

      // 4. Tạo agent entity cơ bản
      const agent = await this.createAgentEntity(createDto, typeAgent, userId);

      // 5. Xử lý các blocks dữ liệu theo TypeAgentConfig
      await this.processAgentBlocks(agent, createDto, typeAgentConfig, userId);

      // 6. Tạo S3 key cho avatar nếu có
      let avatarUploadUrl: string | undefined;
      if (createDto.avatarMimeType) {
        avatarUploadUrl = await this.generateAvatarUploadUrl(agent.id, createDto.avatarMimeType, userId);
      }

      this.logger.log(`Đã tạo agent thành công: ${agent.id}`);

      return {
        id: agent.id,
        avatarUploadUrl: avatarUploadUrl || null,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED, error.message);
    }
  }

  /**
   * Validate dữ liệu agent dựa trên TypeAgentConfig
   * @param createDto DTO tạo agent
   * @param config TypeAgentConfig
   */
  private async validateAgentDataByConfig(
    createDto: CreateAgentDto,
    config: TypeAgentConfig,
  ): Promise<void> {
    // 1. Validate logic model - BẮT BUỘC
    await this.validateModelConfiguration(createDto);

    // 2. Validate các khối theo TypeAgentConfig - CÓ THỂ TRỐNG
    // Chỉ validate khi tính năng được enable và có dữ liệu
    if (config.enableAgentProfileCustomization && createDto.profile) {
      await this.validateProfileBlock(createDto.profile);
    }

    if (config.enableTaskConversionTracking && createDto.conversion) {
      await this.validateConversionBlock(createDto.conversion);
    }

    if (config.enableDynamicStrategyExecution && createDto.strategy) {
      await this.validateStrategyBlock(createDto.strategy);
    }

    if (config.enableMultiAgentCollaboration && createDto.multiAgent) {
      await this.validateMultiAgentBlock(createDto.multiAgent);
    }

    if (config.enableOutputToMessenger && createDto.outputMessenger) {
      await this.validateOutputMessengerBlock(createDto.outputMessenger);
    }

    if (config.enableOutputToWebsiteLiveChat && createDto.outputWebsite) {
      await this.validateOutputWebsiteBlock(createDto.outputWebsite);
    }

    if (config.enableResourceUsage && createDto.resources) {
      await this.validateResourcesBlock(createDto.resources);
    }
  }

  /**
   * Validate logic model configuration
   * @param createDto DTO tạo agent
   */
  private async validateModelConfiguration(createDto: CreateAgentDto): Promise<void> {
    // Kiểm tra khối 1: systemModelId
    const hasSystemModel = createDto.systemModelId && createDto.systemModelId.trim().length > 0;

    // Kiểm tra khối 2: userModelId + keyLlmId
    const hasUserModel = createDto.userModelId && createDto.userModelId.trim().length > 0;
    const hasKeyLlm = createDto.keyLlmId && createDto.keyLlmId.trim().length > 0;
    const hasUserModelBlock = hasUserModel && hasKeyLlm;

    // Phải có ít nhất 1 trong 2 khối
    if (!hasSystemModel && !hasUserModelBlock) {
      throw new AppException(
        AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
        'Bắt buộc phải có 1 trong 2 khối: (systemModelId) hoặc (userModelId + keyLlmId)'
      );
    }

    // Nếu có userModelId thì phải có keyLlmId và ngược lại
    if (hasUserModel && !hasKeyLlm) {
      throw new AppException(
        AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
        'Nếu có userModelId thì phải có keyLlmId'
      );
    }

    if (hasKeyLlm && !hasUserModel) {
      throw new AppException(
        AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
        'Nếu có keyLlmId thì phải có userModelId'
      );
    }
  }

  // Placeholder validation methods - có thể triển khai chi tiết sau
  private async validateProfileBlock(profile: any): Promise<void> {
    // TODO: Implement profile validation
    this.logger.log('Validating profile block');
  }

  private async validateConversionBlock(conversion: any): Promise<void> {
    // TODO: Implement conversion validation
    this.logger.log('Validating conversion block');
  }

  private async validateStrategyBlock(strategy: any): Promise<void> {
    // TODO: Implement strategy validation
    this.logger.log('Validating strategy block');
  }

  private async validateMultiAgentBlock(multiAgent: any): Promise<void> {
    // TODO: Implement multi agent validation
    this.logger.log('Validating multi agent block');
  }

  private async validateOutputMessengerBlock(outputMessenger: any): Promise<void> {
    // TODO: Implement output messenger validation
    this.logger.log('Validating output messenger block');
  }

  private async validateOutputWebsiteBlock(outputWebsite: any): Promise<void> {
    // TODO: Implement output website validation
    this.logger.log('Validating output website block');
  }

  private async validateResourcesBlock(resources: any): Promise<void> {
    // TODO: Implement resources validation
    this.logger.log('Validating resources block');
  }

  /**
   * Tạo agent entity cơ bản
   * @param createDto DTO tạo agent
   * @param typeAgent TypeAgent entity
   * @param userId ID của user
   * @returns Agent entity đã được lưu
   */
  private async createAgentEntity(
    createDto: CreateAgentDto,
    typeAgent: any,
    userId: number,
  ): Promise<Agent> {
    try {
      // Tạo agent entity cơ bản
      const agent = new Agent();
      agent.name = createDto.name;
      agent.modelConfig = createDto.modelConfig;
      agent.instruction = createDto.instruction || null;
      agent.avatar = null; // Sẽ được cập nhật sau khi upload
      agent.vectorStoreId = createDto.vectorStoreId || null;

      // Lưu agent vào database
      const savedAgent = await this.agentRepository.save(agent);

      // Tạo agent user relationship
      const agentUser = new AgentUser();
      agentUser.id = savedAgent.id;
      agentUser.userId = userId;
      agentUser.typeId = createDto.typeId;
      agentUser.profile = createDto.profile ? ProfileMapper.fromDto(createDto.profile) : {};

      // Lưu model fields từ createDto - sử dụng đúng tên trường trong entity
      agentUser.systemModelId = createDto.systemModelId || null;
      agentUser.userModelId = createDto.userModelId || null;
      agentUser.modelFineTuneId = createDto.model_finetuning_id || null;

      // Lưu agent user relationship
      await this.agentUserRepository.save(agentUser);

      this.logger.log(`Đã tạo agent entity: ${savedAgent.id} cho user ${userId}`);
      return savedAgent;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo agent entity: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý các khối cấu hình agent theo TypeAgent config
   * @param agent Agent entity
   * @param createDto Dữ liệu tạo agent
   * @param typeAgentConfig Cấu hình TypeAgent
   * @param userId ID của người dùng
   */
  private async processAgentBlocks(
    agent: Agent,
    createDto: CreateAgentDto,
    typeAgentConfig: TypeAgentConfig,
    userId: number,
  ): Promise<void> {
    try {
      // Xử lý các blocks theo config - chỉ xử lý khi có dữ liệu
      if (typeAgentConfig.enableResourceUsage && createDto.resources) {
        await this.processResourceBlock(agent.id, userId, createDto.resources);
      }

      if (typeAgentConfig.enableDynamicStrategyExecution && createDto.strategy) {
        await this.processStrategyBlock(agent.id, userId, createDto.strategy);
      }

      if (typeAgentConfig.enableMultiAgentCollaboration && createDto.multiAgent) {
        await this.processMultiAgentBlock(agent.id, userId, createDto.multiAgent);
      }

      if (typeAgentConfig.enableOutputToMessenger && createDto.outputMessenger) {
        await this.processOutputMessengerBlock(agent.id, userId, createDto.outputMessenger);
      }

      if (typeAgentConfig.enableOutputToWebsiteLiveChat && createDto.outputWebsite) {
        await this.processOutputWebsiteBlock(agent.id, userId, createDto.outputWebsite);
      }

      this.logger.log(`Đã xử lý các blocks cho agent: ${agent.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý agent blocks: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo URL upload avatar cho agent
   * @param agentId ID của agent
   * @param mimeType MIME type của avatar
   * @param userId ID của user
   * @returns URL upload avatar
   */
  private async generateAvatarUploadUrl(
    agentId: string,
    mimeType: string,
    userId: number,
  ): Promise<string> {
    try {
      // Tạo S3 key cho avatar
      const avatarKey = generateS3Key({
        baseFolder: userId.toString(),
        categoryFolder: CategoryFolderEnum.AGENT,
      });

      // Tạo presigned URL
      const uploadUrl = await this.s3Service.createPresignedWithID(
        avatarKey,
        TimeIntervalEnum.ONE_HOUR,
        ImageType.getType(mimeType),
        FileSizeEnum.FIVE_MB,
      );

      // Cập nhật avatar key cho agent
      await this.agentRepository.update(agentId, { avatar: avatarKey });

      this.logger.log(`Đã tạo avatar upload URL cho agent: ${agentId}`);
      return uploadUrl;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo avatar upload URL: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, error.message);
    }
  }

  // Placeholder process methods - có thể triển khai chi tiết sau
  private async processResourceBlock(agentId: string, userId: number, resources: any): Promise<void> {
    // TODO: Implement resource block processing
    this.logger.log(`Processing resource block for agent ${agentId}`);
  }

  private async processStrategyBlock(agentId: string, userId: number, strategy: any): Promise<void> {
    // TODO: Implement strategy block processing
    this.logger.log(`Processing strategy block for agent ${agentId}`);
  }

  private async processMultiAgentBlock(agentId: string, userId: number, multiAgent: any): Promise<void> {
    // TODO: Implement multi agent block processing
    this.logger.log(`Processing multi agent block for agent ${agentId}`);
  }

  private async processOutputMessengerBlock(agentId: string, userId: number, outputMessenger: any): Promise<void> {
    // TODO: Implement output messenger block processing
    this.logger.log(`Processing output messenger block for agent ${agentId}`);
  }

  private async processOutputWebsiteBlock(agentId: string, userId: number, outputWebsite: any): Promise<void> {
    // TODO: Implement output website block processing
    this.logger.log(`Processing output website block for agent ${agentId}`);
  }
}
