import { ApiResponseDto } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AgentUserService } from '@modules/agent/user/services';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Controller,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiTags
} from '@nestjs/swagger';
import {
  AgentDetailDto,
  AgentListItemDto,
  AgentStatisticsResponseDto,
  CreateAgentResponseDto,
  UpdateAgentResponseDto
} from '../dto';

/**
 * Controller xử lý các API endpoint cho Agent của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  AgentListItemDto,
  AgentDetailDto,
  CreateAgentResponseDto,
  UpdateAgentResponseDto,
  AgentStatisticsResponseDto,
  ApiResponseDto
)
export class AgentUserController {
  constructor(private readonly agentUserService: AgentUserService) { }
}
