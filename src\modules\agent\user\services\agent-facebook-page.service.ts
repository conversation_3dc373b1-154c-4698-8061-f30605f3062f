import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { INTEGRATION_ERROR_CODES } from '@modules/integration/exceptions';
import { AgentUserRepository } from '@modules/agent/repositories';
import { FacebookPageRepository } from '@modules/integration/repositories';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import {
  IntegrateFacebookPageDto,
  AgentFacebookPageListDto,
  AgentFacebookPageDto
} from '../dto/facebook-page';
import { In } from 'typeorm';

/**
 * Service xử lý tích hợp Facebook Page với Agent
 */
@Injectable()
export class AgentFacebookPageService {
  private readonly logger = new Logger(AgentFacebookPageService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly facebookPageRepository: FacebookPageRepository,
    private readonly facebookService: FacebookService,
  ) {}

  /**
   * Tích hợp Facebook Page vào Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param dto Thông tin Facebook Page cần tích hợp (UUID trong hệ thống)
   */
  @Transactional()
  async integrateFacebookPage(
    agentId: string,
    userId: number,
    dto: IntegrateFacebookPageDto,
  ): Promise<void> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra Facebook Page có tồn tại và thuộc về user không (sử dụng UUID)
      const facebookPage = await this.facebookPageRepository.findPageByUserIdAndPageIds(
        userId,
        dto.facebookPageIds // Đây là UUID trong hệ thống
      );

      if (!facebookPage) {
        throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND);
      }

      // Kiểm tra Facebook Page đã được tích hợp với Agent khác chưa
      if (facebookPage.agentId && facebookPage.agentId !== agentId) {
        throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_ALREADY_INTEGRATED);
      }

      // Nếu chưa tích hợp, thực hiện tích hợp
      if (!facebookPage.agentId) {
        // Đăng ký webhook cho Facebook Page
        const subscribeResult = await this.facebookService.subscribeApp(
          facebookPage.facebookPageId,
          facebookPage.pageAccessToken
        );

        if (!subscribeResult.success) {
          throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_SUBSCRIBE_FAILED);
        }

        // Cập nhật agentId cho Facebook Page
        await this.facebookPageRepository.update(facebookPage.id, {
          agentId: agentId,
          isActive: true
        });
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tích hợp Facebook Page vào Agent: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_INTEGRATION_FAILED);
    }
  }

  /**
   * Lấy danh sách Facebook Page trong Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @returns Danh sách Facebook Page
   */
  async getFacebookPages(agentId: string, userId: number): Promise<AgentFacebookPageListDto> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Lấy danh sách Facebook Page đã tích hợp với Agent
      const facebookPages = await this.facebookPageRepository.findByAgentId(agentId);

      // Chuyển đổi sang DTO
      const facebookPageDtos: AgentFacebookPageDto[] = facebookPages.map(page => ({
        id: page.id, // UUID trong hệ thống
        avatarPage: page.avatarPage,
        pageName: page.pageName,
        isActive: page.isActive
      }));

      return {
        facebookPages: facebookPageDtos
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách Facebook Page: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_LIST_FAILED);
    }
  }

  /**
   * Bật/tắt trạng thái active của Facebook Page (toggle)
   * @param agentId ID của Agent
   * @param pageId UUID của Facebook Page trong hệ thống
   * @param userId ID của người dùng
   */
  @Transactional()
  async toggleFacebookPageActive(
    agentId: string,
    pageId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra Facebook Page có tồn tại và đã tích hợp với Agent không (sử dụng UUID)
      const facebookPage = await this.facebookPageRepository.findPageByUserIdAndPageId(userId, pageId);

      if (!facebookPage || facebookPage.agentId !== agentId) {
        throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_INTEGRATED);
      }

      // Đảo ngược trạng thái active
      const newActiveStatus = !facebookPage.isActive;

      // Cập nhật trạng thái
      await this.facebookPageRepository.updateActiveStatus(facebookPage.id, newActiveStatus);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi toggle trạng thái Facebook Page: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_UPDATE_FAILED);
    }
  }

  /**
   * Gỡ Facebook Page khỏi Agent
   * @param agentId ID của Agent
   * @param pageId UUID của Facebook Page trong hệ thống
   * @param userId ID của người dùng
   */
  @Transactional()
  async removeFacebookPage(
    agentId: string,
    pageId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra Facebook Page có tồn tại và đã tích hợp với Agent không (sử dụng UUID)
      const facebookPage = await this.facebookPageRepository.findPageByUserIdAndPageId(userId, pageId);

      if (!facebookPage || facebookPage.agentId !== agentId) {
        throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_INTEGRATED);
      }

      // Hủy đăng ký webhook
      const unsubscribeResult = await this.facebookService.unsubscribeApp(
        facebookPage.facebookPageId,
        facebookPage.pageAccessToken
      );

      if (!unsubscribeResult.success) {
        this.logger.warn(`Không thể hủy đăng ký webhook cho Facebook Page ${pageId}`);
      }

      // Gỡ bỏ agentId khỏi Facebook Page
      await this.facebookPageRepository.update(facebookPage.id, {
        agentId: null,
        isActive: false
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gỡ Facebook Page khỏi Agent: ${error.message}`, error.stack);
      throw new AppException(INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_REMOVE_FAILED);
    }
  }

  /**
   * Kiểm tra Agent có tồn tại và thuộc về user không
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   */
  private async checkAgentOwnership(agentId: string, userId: number): Promise<void> {
    const agentUser = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

    if (!agentUser) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }
  }
}
