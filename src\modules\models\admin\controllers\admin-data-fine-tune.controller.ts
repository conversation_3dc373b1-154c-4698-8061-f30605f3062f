import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  AdminDataFineTuneQueryDto,
  BulkDeleteAdminDataFineTuneDto,
  CreateAdminDataFineTuneDto,
  UpdateAdminDataFineTuneDto
} from '../dto/data-fine-tune';
import { AdminDataFineTuneService } from '../services';

/**
 * Controller xử lý API cho Admin Data Fine Tune
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_FINETUNING_DATA)
@Controller('admin/data-fine-tune')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminDataFineTuneController {
  constructor(private readonly adminDataFineTuneService: AdminDataFineTuneService) { }

  /**
   * Tạo mới dataset fine tune
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới dataset fine tune' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới dataset fine tune thành công',
    type: ApiResponseDto
  })
  create(
    @Body() createDto: CreateAdminDataFineTuneDto,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminDataFineTuneService.create(createDto, employeeId);
  }

  /**
   * Lấy danh sách dataset fine tune có phân trang và tìm kiếm
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách dataset fine tune có phân trang và tìm kiếm',
    description: 'API này hỗ trợ tìm kiếm theo tên dataset, phân trang và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách dataset fine tune',
    type: ApiResponseDto
  })
  findAll(@Query() queryDto: AdminDataFineTuneQueryDto) {
    return this.adminDataFineTuneService.findAll(queryDto);
  }

  /**
   * Lấy URL upload dataset
   */
  @Get('upload-url')
  @ApiOperation({ summary: 'Lấy URL upload dataset' })
  @ApiResponse({
    status: 200,
    description: 'URL upload dataset',
    type: ApiResponseDto
  })
  urlUpload(
    @Query('mime') mime: string,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<{ uploadUrl: string }>> {
    return this.adminDataFineTuneService.urlUpload(employeeId, mime);
  }

  /**
   * Lấy danh sách dataset fine tune đã xóa
   */
  @Get('deleted/list')
  @ApiOperation({ summary: 'Lấy danh sách dataset fine tune đã xóa' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách dataset fine tune đã xóa',
    type: ApiResponseDto
  })
  findDeleted(@Query() queryDto: AdminDataFineTuneQueryDto) {
    return this.adminDataFineTuneService.findDeleted(queryDto);
  }

  /**
   * Lấy chi tiết dataset fine tune
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết dataset fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết dataset fine tune',
    type: ApiResponseDto
  })
  findOne(@Param('id') id: string) {
    return this.adminDataFineTuneService.findOne(id);
  }

  /**
   * Cập nhật dataset fine tune
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật dataset fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật dataset fine tune thành công',
    type: ApiResponseDto
  })
  update(
    @Param('id') id: string,
    @Body() updateDto: UpdateAdminDataFineTuneDto,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminDataFineTuneService.update(id, updateDto, employeeId);
  }

  /**
   * Xóa dataset fine tune (soft delete) - single ID
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa dataset fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Xóa dataset fine tune thành công',
    type: ApiResponseDto
  })
  remove(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminDataFineTuneService.remove([id], employeeId);
  }

  /**
   * Xóa nhiều dataset fine tune (bulk soft delete)
   */
  @Delete('bulk/remove')
  @ApiOperation({ summary: 'Xóa nhiều dataset fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả xóa bulk dataset fine tune',
    type: ApiResponseDto
  })
  bulkRemove(
    @Body() bulkDeleteDto: BulkDeleteAdminDataFineTuneDto,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminDataFineTuneService.remove(bulkDeleteDto.ids, employeeId);
  }

  /**
   * Khôi phục dataset fine tune đã xóa
   */
  @Patch(':id/restore')
  @ApiOperation({ summary: 'Khôi phục dataset fine tune đã xóa' })
  @ApiResponse({
    status: 200,
    description: 'Khôi phục dataset fine tune thành công',
    type: ApiResponseDto
  })
  restore(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminDataFineTuneService.restore(id, employeeId);
  }

  /**
   * Cập nhật trạng thái dataset từ PENDING thành APPROVED hoặc ERROR
   */
  @Patch('status')
  @ApiOperation({ summary: 'Cập nhật trạng thái dataset fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái dataset thành công',
    type: ApiResponseDto
  })
  updateStatus(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminDataFineTuneService.updateStatus(id, employeeId);
  }
}
