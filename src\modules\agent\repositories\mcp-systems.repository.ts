import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { McpSystems } from '@modules/agent/entities';
import { PaginatedResult } from '@/common/response';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho McpSystems
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến MCP systems
 */
@Injectable()
export class McpSystemsRepository extends Repository<McpSystems> {
  private readonly logger = new Logger(McpSystemsRepository.name);

  constructor(private dataSource: DataSource) {
    super(McpSystems, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho McpSystems (chỉ lấy những record chưa bị xóa)
   * @returns SelectQueryBuilder cho McpSystems
   */
  private createBaseQuery(): SelectQueryBuilder<McpSystems> {
    return this.createQueryBuilder('mcpSystems')
      .where('mcpSystems.deletedAt IS NULL');
  }

  /**
   * Tạo query builder cho McpSystems đã bị xóa
   * @returns SelectQueryBuilder cho McpSystems đã xóa
   */
  private createDeletedQuery(): SelectQueryBuilder<McpSystems> {
    return this.createQueryBuilder('mcpSystems')
      .where('mcpSystems.deletedAt IS NOT NULL');
  }

  /**
   * Tìm MCP system theo ID (chỉ những record chưa bị xóa)
   * @param id ID của MCP system
   * @returns McpSystems nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<McpSystems | null> {
    return this.createBaseQuery()
      .where('mcpSystems.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm MCP system theo ID bao gồm cả đã xóa
   * @param id ID của MCP system
   * @returns McpSystems nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdIncludeDeleted(id: string): Promise<McpSystems | null> {
    return this.createQueryBuilder('mcpSystems')
      .where('mcpSystems.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm MCP system theo tên server
   * @param nameServer Tên server của MCP system
   * @returns McpSystems nếu tìm thấy, null nếu không tìm thấy
   */
  async findByNameServer(nameServer: string): Promise<McpSystems | null> {
    return this.createBaseQuery()
      .where('mcpSystems.nameServer = :nameServer', { nameServer })
      .getOne();
  }

  /**
   * Lấy danh sách MCP systems với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách MCP systems với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'nameServer',
    sortDirection: 'ASC' | 'DESC' = 'ASC',
  ): Promise<PaginatedResult<McpSystems>> {
    const qb = this.createBaseQuery();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(mcpSystems.nameServer ILIKE :search OR mcpSystems.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`mcpSystems.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Lấy danh sách tất cả MCP systems
   * @returns Danh sách MCP systems
   */
  async findAll(): Promise<McpSystems[]> {
    return this.createBaseQuery()
      .orderBy('mcpSystems.nameServer', 'ASC')
      .getMany();
  }

  /**
   * Kiểm tra tên server có tồn tại không (trừ ID hiện tại)
   * @param nameServer Tên server cần kiểm tra
   * @param excludeId ID cần loại trừ khỏi kiểm tra
   * @returns true nếu tồn tại, false nếu không
   */
  async isNameServerExists(nameServer: string, excludeId?: string): Promise<boolean> {
    const qb = this.createBaseQuery()
      .where('mcpSystems.nameServer = :nameServer', { nameServer });

    if (excludeId) {
      qb.andWhere('mcpSystems.id != :excludeId', { excludeId });
    }

    const count = await qb.getCount();
    return count > 0;
  }

  /**
   * Soft delete MCP system
   * @param id ID của MCP system cần xóa
   * @param deletedBy ID của nhân viên thực hiện xóa
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async softDeleteCustom(id: string, deletedBy: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .update(McpSystems)
        .set({
          deletedAt: Date.now(),
          deletedBy: deletedBy,
          updatedAt: Date.now(),
          updatedBy: deletedBy,
        })
        .where('id = :id', { id })
        .andWhere('deletedAt IS NULL')
        .execute();

      return (result.affected || 0) > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi soft delete MCP system ${id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Khôi phục MCP system từ trash
   * @param id ID của MCP system cần khôi phục
   * @param restoredBy ID của nhân viên thực hiện khôi phục
   * @returns true nếu khôi phục thành công
   */
  @Transactional()
  async restoreCustom(id: string, restoredBy: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .update(McpSystems)
        .set({
          deletedAt: null,
          deletedBy: null,
          updatedAt: Date.now(),
          updatedBy: restoredBy,
        })
        .where('id = :id', { id })
        .andWhere('deletedAt IS NOT NULL')
        .execute();

      return (result.affected || 0) > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục MCP system ${id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Lấy danh sách MCP systems đã bị xóa với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách MCP systems đã xóa với phân trang
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<McpSystems>> {
    const qb = this.createDeletedQuery();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(mcpSystems.nameServer ILIKE :search OR mcpSystems.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`mcpSystems.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Khôi phục nhiều MCP systems cùng lúc
   * @param ids Danh sách ID của các MCP systems cần khôi phục
   * @param restoredBy ID của nhân viên thực hiện khôi phục
   * @returns Số lượng MCP systems đã khôi phục thành công
   */
  @Transactional()
  async bulkRestore(ids: string[], restoredBy: number): Promise<number> {
    try {
      const result = await this.createQueryBuilder()
        .update(McpSystems)
        .set({
          deletedAt: null,
          deletedBy: null,
          updatedAt: Date.now(),
          updatedBy: restoredBy,
        })
        .where('id IN (:...ids)', { ids })
        .andWhere('deletedAt IS NOT NULL')
        .execute();

      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Lỗi khi bulk restore MCP systems: ${error.message}`, error.stack);
      return 0;
    }
  }
}
