import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  CreateMcpSystemDto,
  UpdateMcpSystemDto,
  McpSystemQueryDto,
  McpSystemListItemDto,
  McpSystemTrashQueryDto,
  McpSystemTrashItemDto,
  RestoreMcpSystemDto,
  RestoreMcpSystemResponseDto,
} from '../dto/mcp-system';
import { McpSystemAdminService } from '../services/mcp-system-admin.service';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';

/**
 * Controller xử lý các endpoint liên quan đến MCP Systems cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_MCP_SYSTEM)
@ApiExtraModels(ApiResponseDto, PaginatedResult, McpSystemListItemDto, McpSystemTrashItemDto, RestoreMcpSystemResponseDto)
@Controller('admin/mcp-systems')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class McpSystemAdminController {
  constructor(private readonly mcpSystemAdminService: McpSystemAdminService) {}

  /**
   * Tạo mới MCP system
   * @param createDto Thông tin MCP system cần tạo
   * @param employee Thông tin nhân viên tạo
   * @returns Thông tin MCP system đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới MCP system' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới MCP system thành công',
    schema: ApiResponseDto.getSchema(McpSystemListItemDto),
  })
  async createMcpSystem(
    @Body() createDto: CreateMcpSystemDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<McpSystemListItemDto>> {
    const result = await this.mcpSystemAdminService.createMcpSystem(createDto, employee.id);
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật MCP system
   * @param id ID của MCP system cần cập nhật
   * @param updateDto Thông tin cần cập nhật
   * @param employee Thông tin nhân viên cập nhật
   * @returns Thông tin MCP system sau khi cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật MCP system' })
  @ApiParam({ name: 'id', description: 'ID của MCP system', type: String })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật MCP system thành công',
    schema: ApiResponseDto.getSchema(McpSystemListItemDto),
  })
  async updateMcpSystem(
    @Param('id') id: string,
    @Body() updateDto: UpdateMcpSystemDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<McpSystemListItemDto>> {
    const result = await this.mcpSystemAdminService.updateMcpSystem(id, updateDto, employee.id);
    return ApiResponseDto.updated(result);
  }

  /**
   * Xóa MCP system
   * @param id ID của MCP system cần xóa
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa MCP system' })
  @ApiParam({ name: 'id', description: 'ID của MCP system', type: String })
  @ApiResponse({
    status: 200,
    description: 'Xóa MCP system thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async deleteMcpSystem(
    @Param('id') id: string,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<boolean>> {
    await this.mcpSystemAdminService.deleteMcpSystem(id, employee.id);
    return ApiResponseDto.deleted(true);
  }

  /**
   * Lấy danh sách MCP systems có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách MCP systems có phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách MCP systems' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách MCP systems thành công',
    schema: ApiResponseDto.getPaginatedSchema(McpSystemListItemDto),
  })
  async getMcpSystems(
    @Query() queryDto: McpSystemQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<McpSystemListItemDto>>> {
    const result = await this.mcpSystemAdminService.getMcpSystems(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy danh sách MCP systems đã xóa có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách MCP systems đã xóa có phân trang
   */
  @Get('trash')
  @ApiOperation({ summary: 'Lấy danh sách MCP systems đã xóa' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách MCP systems đã xóa thành công',
    schema: ApiResponseDto.getPaginatedSchema(McpSystemTrashItemDto),
  })
  async getDeletedMcpSystems(
    @Query() queryDto: McpSystemTrashQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<McpSystemTrashItemDto>>> {
    const result = await this.mcpSystemAdminService.getDeletedMcpSystems(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Khôi phục MCP systems từ trash
   * @param restoreDto Danh sách ID cần khôi phục
   * @param employee Thông tin nhân viên thực hiện khôi phục
   * @returns Kết quả khôi phục
   */
  @Post('restore')
  @ApiOperation({ summary: 'Khôi phục MCP systems từ trash' })
  @ApiResponse({
    status: 200,
    description: 'Khôi phục MCP systems thành công',
    schema: ApiResponseDto.getSchema(RestoreMcpSystemResponseDto),
  })
  async restoreMcpSystems(
    @Body() restoreDto: RestoreMcpSystemDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<RestoreMcpSystemResponseDto>> {
    const result = await this.mcpSystemAdminService.restoreMcpSystems(restoreDto, employee.id);
    return ApiResponseDto.success(result);
  }
}
