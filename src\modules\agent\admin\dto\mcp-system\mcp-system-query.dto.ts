import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của MCP system
 */
export enum McpSystemSortBy {
  NAME_SERVER = 'nameServer',
  DESCRIPTION = 'description',
  ID = 'id',
}

/**
 * DTO cho việc truy vấn danh sách MCP systems
 */
export class McpSystemQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: McpSystemSortBy,
    default: McpSystemSortBy.NAME_SERVER,
  })
  @IsEnum(McpSystemSortBy)
  @IsOptional()
  @Type(() => String)
  sortBy?: McpSystemSortBy = McpSystemSortBy.NAME_SERVER;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
  })
  @IsEnum(SortDirection)
  @IsOptional()
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.ASC;
}
