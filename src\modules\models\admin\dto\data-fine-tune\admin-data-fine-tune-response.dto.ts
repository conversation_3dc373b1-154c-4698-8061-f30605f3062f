import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';
import { ProviderFineTuneEnum } from '@/modules/models/constants';
import { DataFineTuneStatus } from '@/modules/models/constants/data-fine-tune-status.enum';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO response cho admin data fine-tune
 */
export class AdminDataFineTuneResponseDto {
  @ApiProperty({
    description: 'ID của bộ dữ liệu',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Tên bộ dữ liệu',
    example: 'Customer Service Training Dataset'
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả về bộ dữ liệu',
    example: 'Bộ dữ liệu huấn luyện cho chatbot hỗ trợ khách hàng',
    nullable: true
  })
  description: string | null;

  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderFineTuneEnum,
    example: ProviderFineTuneEnum.OPENAI,
  })
  provider: ProviderFineTuneEnum;

  @ApiProperty({
    description: 'Trạng thái của bộ dữ liệu',
    enum: DataFineTuneStatus,
    example: DataFineTuneStatus.PENDING,
  })
  status: DataFineTuneStatus;

  @ApiProperty({
    description: 'Thời gian tạo (epoch timestamp)',
    example: 1703980800000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (epoch timestamp)',
    example: 1703980800000
  })
  updatedAt: number;
}

/**
 * DTO response cho admin data fine-tune với dữ liệu training
 */
export class AdminDataFineTuneDetailResponseDto extends AdminDataFineTuneResponseDto {
  @ApiProperty({
    description: 'Dữ liệu huấn luyện',
    example: 'https://s3.amazonaws.com/bucket/path/to/dataset.jsonl'
  })
  trainDataset: string | null;

  @ApiProperty({
    description: 'Dữ liệu kiểm định',
    example: 'https://s3.amazonaws.com/bucket/path/to/dataset.jsonl',
    nullable: true
  })
  validDataset: string | null;

  @ApiProperty({
    description: 'Thông tin người tạo',
    type: EmployeeInfoSimpleDto,
  })
  created: EmployeeInfoSimpleDto | null;

  @ApiProperty({
    description: 'Thông tin người cập nhật',
    type: EmployeeInfoSimpleDto,
    nullable: true,
  })
  updated: EmployeeInfoSimpleDto | null;
}
