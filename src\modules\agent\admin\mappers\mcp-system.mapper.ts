import { McpSystems } from '@modules/agent/entities';
import { McpSystemListItemDto, McpSystemTrashItemDto } from '../dto/mcp-system';

/**
 * Mapper cho MCP System
 * Chuyển đổi giữa Entity và DTO
 */
export class McpSystemMapper {
  /**
   * Chuyển đổi từ Entity sang ListItemDto
   * @param entity McpSystems entity
   * @returns McpSystemListItemDto
   */
  static toListItemDto(entity: McpSystems): McpSystemListItemDto {
    return {
      id: entity.id,
      nameServer: entity.nameServer,
      description: entity.description,
      config: entity.config,
    };
  }

  /**
   * Chuyển đổi từ danh sách Entity sang danh sách ListItemDto
   * @param entities Danh sách McpSystems entities
   * @returns Danh sách McpSystemListItemDto
   */
  static toListItemDtos(entities: McpSystems[]): McpSystemListItemDto[] {
    return entities.map(entity => this.toListItemDto(entity));
  }

  /**
   * <PERSON>yển đổi từ Entity sang TrashItemDto
   * @param entity McpSystems entity đã bị xóa
   * @returns McpSystemTrashItemDto
   */
  static toTrashItemDto(entity: McpSystems): McpSystemTrashItemDto {
    return {
      id: entity.id,
      nameServer: entity.nameServer,
      description: entity.description,
      config: entity.config,
      deletedAt: entity.deletedAt!,
      deletedBy: entity.deletedBy,
    };
  }

  /**
   * Chuyển đổi từ danh sách Entity sang danh sách TrashItemDto
   * @param entities Danh sách McpSystems entities đã xóa
   * @returns Danh sách McpSystemTrashItemDto
   */
  static toTrashItemDtos(entities: McpSystems[]): McpSystemTrashItemDto[] {
    return entities.map(entity => this.toTrashItemDto(entity));
  }
}
