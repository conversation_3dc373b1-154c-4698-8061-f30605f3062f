import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của MCP system đã xóa
 */
export enum McpSystemTrashSortBy {
  NAME_SERVER = 'nameServer',
  DESCRIPTION = 'description',
  DELETED_AT = 'deletedAt',
  ID = 'id',
}

/**
 * DTO cho việc truy vấn danh sách MCP systems đã xóa
 */
export class McpSystemTrashQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: McpSystemTrashSortBy,
    default: McpSystemTrashSortBy.DELETED_AT,
  })
  @IsEnum(McpSystemTrashSortBy)
  @IsOptional()
  sortBy?: McpSystemTrashSortBy = McpSystemTrashSortBy.DELETED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsEnum(SortDirection)
  @IsOptional()
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.DESC;
}
