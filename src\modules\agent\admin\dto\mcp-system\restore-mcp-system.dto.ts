import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc khôi phục MCP systems từ trash
 */
export class RestoreMcpSystemDto {
  /**
   * Danh sách ID của các MCP systems cần khôi phục
   */
  @ApiProperty({
    description: 'Danh sách ID của các MCP systems cần khôi phục',
    example: ['123e4567-e89b-12d3-a456-426614174000', '456e7890-e89b-12d3-a456-426614174001'],
    type: [String],
  })
  @IsNotEmpty({ message: 'Danh sách ID không được để trống' })
  @IsArray({ message: 'IDs phải là một mảng' })
  @IsString({ each: true, message: 'Mỗi ID phải là chuỗi' })
  ids: string[];
}
