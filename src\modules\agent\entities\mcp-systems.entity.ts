import { Column, Entity, PrimaryGeneratedColumn, CreateDateColumn, BeforeUpdate } from 'typeorm';

/**
 * Entity đại diện cho bảng mcp_systems trong cơ sở dữ liệu
 * Quản lý các hệ thống MCP (Model Context Protocol)
 */
@Entity('mcp_systems')
export class McpSystems {
  /**
   * UUID của MCP system
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên server MCP
   */
  @Column({
    name: 'name_server',
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Tên server MCP'
  })
  nameServer: string;

  /**
   * Mô tả về MCP system
   */
  @Column({
    type: 'text',
    nullable: true,
    comment: 'Mô tả về MCP system'
  })
  description?: string;

  /**
   * Cấu hình MCP dạng JSONB
   */
  @Column({
    type: 'jsonb',
    comment: '<PERSON><PERSON><PERSON> hình MCP dạng JSONB'
  })
  config: Record<string, any>;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @CreateDateColumn({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
  })
  updatedAt: number;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }

  /**
   * Thời điểm xóa mềm (timestamp millis)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * ID nhân viên thực hiện xóa
   */
  @Column({ name: 'deleted_by', type: 'integer', nullable: true })
  deletedBy: number | null;

  /**
   * ID nhân viên tạo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID nhân viên cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;
}
