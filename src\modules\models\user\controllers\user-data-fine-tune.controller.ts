import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { CreateUserDataFineTuneDto } from '../dto/user-data-fine-tune/create-user-data-fine-tune.dto';
import { UserDataFineTuneQueryDto } from '../dto/user-data-fine-tune/user-data-fine-tune-query.dto';
import { UserDataFineTuneResponseDto } from '../dto/user-data-fine-tune/user-data-fine-tune-response.dto';
import { UserDataFineTuneService } from '../services/user-data-fine-tune.service';

/**
 * Controller xử lý API cho User Data Fine Tune
 */
@ApiTags(SWAGGER_API_TAGS.USER_FINETUNING_DATA)
@Controller('user/data-fine-tune')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserDataFineTuneController {
  constructor(private readonly userDataFineTuneService: UserDataFineTuneService) { }

  /**
   * Tạo mới dataset fine tune
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới dataset fine tune' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới dataset fine tune thành công',
    type: ApiResponseDto
  })
  create(
    @Body() createDto: CreateUserDataFineTuneDto, // TODO: Tạo CreateUserDataFineTuneDto
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<{ id: string, trainUploadUrl: string, validUploadUrl: string | null }>> {
    return this.userDataFineTuneService.create(userId, createDto);
  }

  /**
   * Cập nhật trạng thái upload dataset
   */
  @Patch(':id/upload-url-success')
  @ApiOperation({ summary: 'Cập nhật trạng thái upload dataset' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái dataset thành công',
    type: ApiResponseDto
  })
  updateStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<{ id: string }>> {
    return this.userDataFineTuneService.updateStatus(userId, id);
  }

  /**
   * Lấy danh sách dataset fine tune của user có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách dataset fine tune của user có phân trang',
    description: 'API này hỗ trợ tìm kiếm theo tên dataset, phân trang và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách dataset fine tune',
    type: ApiResponseDto
  })
  findAll(
    @CurrentUser('id') userId: number,
    @Query() queryDto: UserDataFineTuneQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<UserDataFineTuneResponseDto>>> {
    return this.userDataFineTuneService.findAll(userId, queryDto);
  }

  /**
   * Lấy chi tiết dataset fine tune
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết dataset fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết dataset fine tune',
    type: ApiResponseDto
  })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userDataFineTuneService.findOne(userId, id);
  }

  /**
   * Cập nhật dataset fine tune
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật dataset fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật dataset fine tune thành công',
    type: ApiResponseDto
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: any, // TODO: Tạo UpdateUserDataFineTuneDto
    @CurrentUser('id') userId: number
  ) {
    return this.userDataFineTuneService.update(userId, id, updateDto);
  }

  /**
   * Xóa dataset fine tune
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa dataset fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Xóa dataset fine tune thành công',
    type: ApiResponseDto
  })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userDataFineTuneService.remove(userId, id);
  }

  /**
   * Lấy URL upload dataset
   */
  @Get('upload-url')
  @ApiOperation({ summary: 'Lấy URL upload dataset' })
  @ApiResponse({
    status: 200,
    description: 'URL upload dataset',
    type: ApiResponseDto
  })
  urlUpload(
    @Query('mime') mime: string,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<{ uploadUrl: string }>> {
    return this.userDataFineTuneService.urlUpload(userId, mime);
  }
}
