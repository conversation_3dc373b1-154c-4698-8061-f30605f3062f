import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo mới MCP system
 */
export class CreateMcpSystemDto {
  /**
   * Tên server MCP
   */
  @ApiProperty({
    description: 'Tên server MCP',
    example: 'filesystem-server',
  })
  @IsNotEmpty({ message: 'Tên server MCP không được để trống' })
  @IsString({ message: 'Tên server MCP phải là chuỗi' })
  nameServer: string;

  /**
   * Mô tả về MCP system
   */
  @ApiPropertyOptional({
    description: 'Mô tả về MCP system',
    example: 'Server MCP để quản lý hệ thống file',
  })
  @IsOptional()
  @IsString({ message: '<PERSON>ô tả phải là chuỗi' })
  description?: string;

  /**
   * <PERSON><PERSON>u hình MCP dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình MCP dạng JSON',
    example: {
      command: 'node',
      args: ['server.js'],
      env: {
        NODE_ENV: 'production'
      }
    },
  })
  @IsNotEmpty({ message: 'Cấu hình MCP không được để trống' })
  @IsObject({ message: 'Cấu hình MCP phải là object' })
  config: Record<string, any>;
}
